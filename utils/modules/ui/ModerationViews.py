"""
UI Views and components for moderation panel functionality.

This module contains the View classes used for target selection and hub selection
in the moderation panel system.
"""

from collections.abc import Sequence
import discord
from discord.ui import View, button, Button, Select
from typing import Optional, TYPE_CHECKING

from utils.modules.core.db.models import Hub
from utils.modules.ui.CreateEmbed import create_embed
from utils.modules.core.checks import interaction_check

FALLBACK_URL = "https://interchat.tech/404"

class TargetSelectionView(View):
    """View for selecting between user and server targets."""

    def __init__(
        self,
        bot,
        moderator: discord.User | discord.Member,
        target_user: discord.User | discord.Member,
        target_server: discord.Guild | None,
        message: Optional[discord.Message],
        constants,
        locale: str,
        selected_action: Optional[str] = None,
        hub_id: Optional[str] = None,
        selected_hub: Optional[Hub] = None,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.moderator = moderator
        self.target_user = target_user
        self.target_server = target_server
        self.message = message
        self.constants = constants
        self.locale = locale
        self.selected_action = selected_action
        self.hub_id = hub_id
        self.selected_hub = selected_hub

    @button(label="User", style=discord.ButtonStyle.primary, emoji="👤")
    async def select_user(self, interaction: discord.Interaction, button: Button):
        """Select user as the target."""
        if not await interaction_check(interaction, self.moderator, interaction.user):
            return

        # Import here to avoid circular imports
        from cogs.modules.hubModeration import ModerationButtons

        # Create moderation panel for user
        embed = create_embed(
            self.locale,
            title_key="modPanel.embed.title",
            description_key="modPanel.embed.description",
            fields=[
                {
                    "name_key": "modPanel.embed.fields.data.name",
                    "value_key": "modPanel.embed.fields.data.value",
                },
                {
                    "name_key": "modPanel.embed.fields.user.name",
                    "value_key": "modPanel.embed.fields.user.value",
                },
            ],
            messageContent=self.message.content if self.message else "N/A",
            messageLink=self.message.jump_url if self.message else FALLBACK_URL,
            serverName=self.target_server.name if self.target_server else "N/A",
            serverId=str(self.target_server.id if self.target_server else "N/A"),
            hubName=self.selected_hub.name if self.selected_hub else "N/A",
            userId=str(self.target_user.id),
            reputation=0,
            infractions=0,  # TODO: Get actual infraction count
        )
        embed.set_author(
            name=f"@{self.moderator.name}", icon_url=self.moderator.display_avatar.url
        )

        view = ModerationButtons(
            self.bot,
            self.moderator,
            self.constants,
            self.locale,
            self.target_user,
            self.message,
            target_server=self.target_server,
            selected_hub=self.selected_hub,
            hub_id=self.hub_id,
        )

        # Set target selection flag
        view._target_selected = "user"

        # Create a mock context object for setup_options
        class MockContext:
            def __init__(self, interaction):
                self.author = interaction.user
                self.bot = interaction.client

        mock_ctx = MockContext(interaction)
        await view.setup_options(
            mock_ctx, self.message.content if self.message else "N/A"
        )
        await interaction.response.edit_message(embed=embed, view=view)

    @button(label="Server", style=discord.ButtonStyle.secondary, emoji="🏢")
    async def select_server(self, interaction: discord.Interaction, button: Button):
        """Select server as the target."""
        if not await interaction_check(interaction, self.moderator, interaction.user):
            return

        # Import here to avoid circular imports
        from cogs.modules.hubModeration import ModerationButtons

        # Create moderation panel for server
        embed = create_embed(
            self.locale,
            title_key="modPanel.embed.title",
            description_key="modPanel.embed.description",
            fields=[
                {
                    "name_key": "modPanel.embed.fields.data.name",
                    "value_key": "modPanel.embed.fields.data.value",
                },
                {
                    "name_key": "modPanel.embed.fields.user.name",
                    "value_key": "modPanel.embed.fields.user.value",
                },
            ],
            messageContent=self.message.content if self.message else "N/A",
            messageLink=self.message.jump_url if self.message else FALLBACK_URL,
            serverName=self.target_server.name if self.target_server else "N/A",
            serverId=str(self.target_server.id if self.target_server else "N/A"),
            hubName=self.selected_hub.name if self.selected_hub else "N/A",
            userId=str(self.target_user.id),
            reputation=0,
            infractions=0,  # TODO: Get actual infraction count
        )
        embed.set_author(
            name=f"@{self.moderator.name}", icon_url=self.moderator.display_avatar.url
        )

        # For server moderation, we pass the server as the target_server parameter
        view = ModerationButtons(
            self.bot,
            self.moderator,
            self.constants,
            self.locale,
            None,  # No user target for server moderation
            self.message,
            target_server=self.target_server,
            selected_hub=self.selected_hub,
            hub_id=self.hub_id,
        )

        # Set target selection flag
        view._target_selected = "server"

        # Create a mock context object for setup_options
        class MockContext:
            def __init__(self, interaction):
                self.author = interaction.user
                self.bot = interaction.client

        mock_ctx = MockContext(interaction)
        await view.setup_options(
            mock_ctx, self.message.content if self.message else "N/A"
        )
        await interaction.response.edit_message(embed=embed, view=view)


class HubSelectionView(View):
    """View for selecting a hub when no message is provided."""

    def __init__(
        self,
        bot,
        moderator: discord.User | discord.Member,
        target_user: Optional[discord.User | discord.Member],
        target_server: Optional[discord.Guild],
        user_hubs: Sequence[Hub],
        constants,
        locale: str,
    ):
        super().__init__(timeout=300)
        self.bot = bot
        self.moderator = moderator
        self.target_user = target_user
        self.target_server = target_server
        self.user_hubs = user_hubs
        self.constants = constants
        self.locale = locale
        self.selected_hub = None

        # Add hub selection dropdown
        self.add_item(HubSelectDropdown(self.user_hubs, self))

    async def update_with_hub(
        self, interaction: discord.Interaction, selected_hub: Hub
    ):
        """Update the view after a hub is selected."""
        self.selected_hub = selected_hub

        # Import here to avoid circular imports
        from cogs.modules.hubModeration import ModerationButtons

        # Create moderation buttons for the selected hub
        view = ModerationButtons(
            self.bot,
            self.moderator,
            self.constants,
            self.locale,
            self.target_user,
            None,  # No message
            target_server=self.target_server,
            selected_hub=selected_hub,
            hub_id=selected_hub.id,  # Pass hub ID directly
        )

        # Create a mock context object for setup_options
        class MockContext:
            def __init__(self, interaction):
                self.author = interaction.user
                self.bot = interaction.client

        mock_ctx = MockContext(interaction)
        await view.setup_options(mock_ctx, "N/A")

        # Create the unified moderation panel embed using the locale system
        embed = create_embed(
            self.locale,
            title_key="modPanel.embed.title",
            description_key="modPanel.embed.description",
            fields=[
                {
                    "name_key": "modPanel.embed.fields.data.name",
                    "value_key": "modPanel.embed.fields.data.value",
                },
                {
                    "name_key": "modPanel.embed.fields.user.name",
                    "value_key": "modPanel.embed.fields.user.value",
                },
            ],
            messageContent="N/A",
            messageLink=FALLBACK_URL,
            serverName=self.target_server.name if self.target_server else "N/A",
            serverId=str(self.target_server.id) if self.target_server else "N/A",
            hubName=selected_hub.name,
            userId=str(self.target_user.id) if self.target_user else "N/A",
            reputation=0,  # TODO: Get actual reputation
            infractions=0,  # TODO: Get actual infraction count
        )
        embed.set_author(
            name=f"@{self.moderator.name}", icon_url=self.moderator.display_avatar.url
        )

        await interaction.response.edit_message(embed=embed, view=view)


class HubSelectDropdown(Select):
    """Dropdown for selecting a hub."""

    def __init__(
        self,
        hubs: Sequence[Hub],
        parent_view: HubSelectionView,
        selected_hub_id: Optional[str] = None,
    ):
        options = []
        for hub in hubs[:25]:  # Discord limit of 25 options
            options.append(
                discord.SelectOption(
                    label=hub.name,
                    value=hub.id,
                    description=(
                        hub.shortDescription[:100]
                        if hub.shortDescription
                        else "No description"
                    ),
                    default=(hub.id == selected_hub_id) if selected_hub_id else False,
                )
            )

        super().__init__(
            placeholder="Select a hub...", options=options, min_values=1, max_values=1
        )
        self.hubs = hubs
        self.parent_view = parent_view

    async def callback(self, interaction: discord.Interaction):
        """Handle hub selection."""
        if not await interaction_check(
            interaction, self.parent_view.moderator, interaction.user
        ):
            return

        selected_hub_id = self.values[0]
        selected_hub = next(
            (hub for hub in self.hubs if hub.id == selected_hub_id), None
        )

        if selected_hub:
            await self.parent_view.update_with_hub(interaction, selected_hub)
