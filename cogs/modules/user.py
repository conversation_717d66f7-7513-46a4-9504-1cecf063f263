import discord
from discord.ext import commands
from discord.ui import View, button, Button

from sqlalchemy import select

from utils.modules.ui.HtmlGeneration import generate_profile, generate_leaderboard
from utils.modules.core.checks import is_interchat_staff, interaction_check
from utils.modules.core.db.models import User as DbUser, Hub
from utils.utils import parse_discord_emoji, fetch_badges, fetch_achievements, check_user
from utils.constants import InterchatConstants
from utils.utils import parse_discord_emoji, fetch_badges
from utils.constants import InterchatConstants

from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from main import Bot

class UserButtons(View):
    def __init__(self, bot, user):
        super().__init__(timeout=300)
        self.bot = bot
        self.user = user

    def setup_button(self):
        self.callback.emoji = self.bot.emotes.trophy_icon

    @button(
        style=discord.ButtonStyle.grey
    )
    async def callback(self, interaction: discord.Interaction, button: Button):
        await interaction.response.defer()
        if not await interaction_check(interaction, self.user, interaction.user):
            return

        await interaction.followup.send('achivements here', ephemeral=True)

class User(commands.Cog):
    def __init__(self, bot):
        self.bot: Bot = bot
        self.constants = InterchatConstants()

    @commands.hybrid_command(name='profile', description='View a users InterChat profile', extras={'category': 'General'})
    @check_user()
    async def profile(self, ctx: commands.Context, user: discord.User = None):
        achievements = [] 

        if user is None:
            user = ctx.author

        def_achievements = [
            {
                "icon": parse_discord_emoji(self.bot.emotes.x_icon),
                "title": "None Found",
                "description": "I could not find any achivements for this user!"
            }
        ]

        def_badges = [
            {
                "icon": parse_discord_emoji(self.bot.emotes.x_icon),
                "title": "None Found",
                "description": "I could not find any badges for this user!"
            }
        ]

        achievements = await fetch_achievements(self.bot, ctx, user, 6)
        badges = await fetch_badges(self.bot, ctx, self.constants, user)

        if badges == []:
            badges = def_badges

        if achievements == []:
            achievements = def_achievements


        buffer = await generate_profile(self.bot, ctx, achievements, badges, user)
        await ctx.send(file=discord.File(buffer, filename=f'{ctx.author.id}_profile.png'))

    @commands.hybrid_command(name='achievements', description='View a users InterChat achievements', extras={'category': 'General'})
    async def achievements(self, ctx: commands.Context, user: discord.User = None):
        ...

    @commands.hybrid_command(name='leaderboard', description='View the InterChat leaderboard', extras={'category': 'General'})
    @check_user()
    async def leaderboard(self, ctx: commands.Context):
        async with self.bot.db.get_session() as session:
            stmt = select(DbUser).order_by(DbUser.messageCount.desc()).limit(10)
            result = (await session.execute(stmt)).scalars().all()
        
        leaderboard_data = []
        
        cur_rank = 0

        for user in result:
            cur_rank += 1
            discord_user = await self.bot.fetch_user(int(user.id))
            is_staff = await is_interchat_staff(ctx, discord_user)
            leaderboard_data.append({
                'rank': cur_rank,
                'username': f"@{user.name}",
                'guild_tag': 'InterChat Staff' if is_staff else 'User',
                'avatar_url': user.image if user.image else self.bot.avatar_url,
                'stat1_value': user.messageCount,
                'stat2_value': user.voteCount,
            })

        buffer = await generate_leaderboard(self.bot, ctx, leaderboard_data, "Messages", "Vote Count")
        await ctx.send(file=discord.File(buffer, filename='leaderboard.png'))

    @commands.hybrid_group()
    async def my(self, ctx: commands.Context):
        pass

    @my.command(name='hubs', description='View all InterChat hubs you moderate, or own', extras={'category': 'Hubs'})
    async def hubs(self, ctx: commands.Context):
        async with self.bot.db.get_session() as session:
            stmt = (select(Hub).where(Hub.moderators.any(userId=str(ctx.author.id))))
            result = (await session.execute(stmt)).scalars().all()

        embed = discord.Embed(title='Your Hubs', description=' ', color=self.constants.color())
        embed.set_author(name=f"@{ctx.author.name}", icon_url=ctx.author.display_avatar.url)
        
        for hub in result:
            user_role = "Unknown"
            for moderator in hub.moderators:
                if moderator.userId == str(ctx.author.id):
                    user_role = moderator.role
                    break
            
            embed.add_field(name=hub.name, value=f'> **Description:** {hub.description}\n> **Position:** {user_role}', inline=False)
        await ctx.send(embed=embed)

async def setup(bot):
    await bot.add_cog(User(bot))